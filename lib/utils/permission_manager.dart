import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';

/// 权限管理器
/// 负责处理iOS和Android的各种权限请求
class PermissionManager {
  
  /// 请求相机权限
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }
  
  /// 请求相册权限
  static Future<bool> requestPhotosPermission() async {
    final status = await Permission.photos.request();
    return status == PermissionStatus.granted;
  }
  
  /// 请求通知权限
  static Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status == PermissionStatus.granted;
  }
  
  /// 检查相机权限状态
  static Future<PermissionStatus> getCameraPermissionStatus() async {
    return await Permission.camera.status;
  }
  
  /// 检查相册权限状态
  static Future<PermissionStatus> getPhotosPermissionStatus() async {
    return await Permission.photos.status;
  }
  
  /// 检查通知权限状态
  static Future<PermissionStatus> getNotificationPermissionStatus() async {
    return await Permission.notification.status;
  }
  
  /// 显示权限被拒绝的对话框
  static void showPermissionDeniedDialog(
    BuildContext context,
    String permissionName,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('需要$permissionName权限'),
          content: Text(description),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                openAppSettings();
              },
              child: const Text('去设置'),
            ),
          ],
        );
      },
    );
  }
  
  /// 检查并请求相机权限
  static Future<bool> checkAndRequestCameraPermission(BuildContext context) async {
    final status = await getCameraPermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.denied:
        final result = await requestCameraPermission();
        if (!result) {
          showPermissionDeniedDialog(
            context,
            '相机',
            '需要相机权限来拍摄头像照片。请在设置中允许访问相机。',
          );
        }
        return result;
      case PermissionStatus.permanentlyDenied:
        showPermissionDeniedDialog(
          context,
          '相机',
          '相机权限已被永久拒绝。请在设置中手动开启相机权限。',
        );
        return false;
      default:
        return false;
    }
  }
  
  /// 检查并请求相册权限
  static Future<bool> checkAndRequestPhotosPermission(BuildContext context) async {
    final status = await getPhotosPermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.denied:
        final result = await requestPhotosPermission();
        if (!result) {
          showPermissionDeniedDialog(
            context,
            '相册',
            '需要相册权限来选择头像图片。请在设置中允许访问相册。',
          );
        }
        return result;
      case PermissionStatus.permanentlyDenied:
        showPermissionDeniedDialog(
          context,
          '相册',
          '相册权限已被永久拒绝。请在设置中手动开启相册权限。',
        );
        return false;
      default:
        return false;
    }
  }
  
  /// 检查并请求通知权限
  static Future<bool> checkAndRequestNotificationPermission(BuildContext context) async {
    final status = await getNotificationPermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.denied:
        final result = await requestNotificationPermission();
        if (!result) {
          showPermissionDeniedDialog(
            context,
            '通知',
            '需要通知权限来发送每日塔罗提醒。请在设置中允许发送通知。',
          );
        }
        return result;
      case PermissionStatus.permanentlyDenied:
        showPermissionDeniedDialog(
          context,
          '通知',
          '通知权限已被永久拒绝。请在设置中手动开启通知权限。',
        );
        return false;
      default:
        return false;
    }
  }
  
  /// 一次性请求所有必要权限
  static Future<Map<String, bool>> requestAllPermissions(BuildContext context) async {
    final results = <String, bool>{};
    
    // 请求相机权限
    results['camera'] = await checkAndRequestCameraPermission(context);
    
    // 请求相册权限
    results['photos'] = await checkAndRequestPhotosPermission(context);
    
    // 请求通知权限
    results['notification'] = await checkAndRequestNotificationPermission(context);
    
    return results;
  }
  
  /// 检查所有权限状态
  static Future<Map<String, PermissionStatus>> checkAllPermissions() async {
    return {
      'camera': await getCameraPermissionStatus(),
      'photos': await getPhotosPermissionStatus(),
      'notification': await getNotificationPermissionStatus(),
    };
  }
}
