import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/screens/question_input_screen.dart';
import 'package:ai_tarot_reading/screens/card_shuffle_screen.dart';
import 'package:ai_tarot_reading/screens/topic_question_screen.dart';
import 'package:ai_tarot_reading/screens/topic_demo_screen.dart';
import 'package:ai_tarot_reading/widgets/rolling_topic_gallery.dart';
import 'package:ai_tarot_reading/widgets/shape_blur_button.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

import 'package:provider/provider.dart';
import 'dart:math' as math;

class TarotMainPage extends StatelessWidget {
  const TarotMainPage({super.key});

  void _navigateToQuestionInput(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const QuestionInputScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      body: Stack(
        children: [
          // 🖼️ 吉祥物图片背景层 - 撑大显示，不足部分白色衔接
          Positioned.fill(
            child: Container(
              color: Colors.white, // 白色底色作为衔接
              child: Image.asset(
                'assets/images/tarot_mascot.png',
                fit: BoxFit.cover, // 撑大覆盖整个屏幕
                errorBuilder: (context, error, stackTrace) {
                  // 如果图片加载失败，回退到原来的渐变背景
                  return Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment(-1.0, -1.0), // 135deg
                        end: Alignment(1.0, 1.0),
                        stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                        colors: [
                          Color(0xFF87CEEB), // #87CEEB 0%
                          Color(0xFFE6E6FA), // #E6E6FA 25%
                          Color(0xFFF8BBD9), // #F8BBD9 50%
                          Color(0xFFE6E6FA), // #E6E6FA 75%
                          Color(0xFF87CEEB), // #87CEEB 100%
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),




          
          // 内容层 - 使用CustomScrollView实现整页滑动
          CustomScrollView(
            slivers: [
              // 主页内容区域
              SliverToBoxAdapter(
                child: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  child: Stack(
                    children: [
                // 🎭 前景吉祥物 - 在文字上方但不重叠，适当放大
                // 注意：cloud_background.png 实际上是被命名错误的吉祥物图片
                Positioned(
                  left: 0,
                  right: 0,
                  top: MediaQuery.of(context).padding.top - 30, // 往上移动更多
                  bottom: MediaQuery.of(context).size.height * 0.58, // 增加底部空间，给文字留更多空隙
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.02, // 减少水平边距，让图片更大
                    ),
                    child: Transform.scale(
                      scale: 1.3, // 放大1.3倍，让图片更显眼
                      child: Image.asset(
                        'assets/images/cloud_background.png', // 实际是吉祥物图片，命名有误
                        fit: BoxFit.contain, // 保持比例，完整显示在容器内
                        alignment: Alignment.center, // 居中显示
                        errorBuilder: (context, error, stackTrace) {
                          // 如果吉祥物图片加载失败，显示透明容器
                          return Container(color: Colors.transparent);
                        },
                      ),
                    ),
                  ).animate(
                    onPlay: (controller) => controller.repeat(reverse: true),
                  ).moveY(
                    begin: 0,
                    end: -8,
                    duration: 3000.ms, // 轻柔的浮动动画
                    curve: Curves.easeInOut,
                  ),
                ),

                // 🌟 激励文字 - 分层显示，白色文字
                Positioned(
                  left: MediaQuery.of(context).size.width * 0.08,
                  right: MediaQuery.of(context).size.width * 0.08,
                  top: MediaQuery.of(context).size.height * 0.38, // 往上移动到38%位置
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            languageManager.translate('intro1'), // "Destiny is a canvas"
                            style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            languageManager.translate('intro2'), // "woven from belief and awareness."
                            style: const TextStyle(
                              fontSize: 18,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            languageManager.translate('intro3'), // "Tarot shows you where you are —"
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black87,
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 20),
                          Text(
                            languageManager.translate('intro4'), // "With every draw,"
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            languageManager.translate('intro5'), // "you see clearer,"
                            style: const TextStyle(fontSize: 16, color: Colors.black),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            languageManager.translate('intro6'), // "release deeper,"
                            style: const TextStyle(fontSize: 16, color: Colors.black),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            languageManager.translate('intro7'), // "and manifest better."
                            style: const TextStyle(fontSize: 16, color: Colors.black),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      );
                    },
                  ),
                ),
                
                // 🔘 开始改变按钮 - 带特效的自定义按钮，向下移动
                Positioned(
                  left: MediaQuery.of(context).size.width * 0.1, // 左右各留10%边距
                  right: MediaQuery.of(context).size.width * 0.1,
                  top: MediaQuery.of(context).size.height * 0.76, // 向下移动到76%位置
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return ShapeBlurButton(
                        text: languageManager.translate('chat_with_higher_self'),
                        width: double.infinity,
                        height: math.max(56, MediaQuery.of(context).size.height * 0.08),
                        baseColor: const Color(0xFF8B5CF6),
                        textColor: Colors.white,
                        shapeSize: 1.0,
                        roundness: 0.5,
                        borderSize: 0.03,
                        circleSize: 0.4,
                        circleEdge: 0.6,
                        onPressed: () {
                          _navigateToQuestionInput(context);
                        },
                      );
                    },
                  ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.5, end: 0),
                ),

                    ],
                  ),
                ),
              ),



              // 专题选择标题
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Column(
                        children: [
                          Text(
                            languageManager.translate('choose_your_topic'),
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.w700,
                              color: Color(0xFF2D3748),
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            languageManager.translate('swipe_or_drag_for_more'),
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),

              // 3D滚动专题选择
              SliverToBoxAdapter(
                child: Container(
                  height: 550, // 增加高度以显示完整卡片
                  margin: const EdgeInsets.only(top: 20), // 向上移动
                  child: const RollingTopicGallery(
                    autoplay: true,
                    pauseOnHover: true,
                  ),
                ),
              ),


              // 底部留白
              SliverToBoxAdapter(
                child: const SizedBox(height: 100),
              ),


            ],
          ),
        ],
      ),
    );
  }

  // 构建带图片的专题卡片
  Widget _buildTopicCardWithImage(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String imagePath,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              // 半透明液体玻璃苹果风效果
              color: Colors.white.withValues(alpha: 0.85),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1.5,
              ),
              boxShadow: [
                // 主要阴影 - 更柔和
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                // 次要阴影 - 增加深度
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              children: [
                // 左侧图片区域
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: const LinearGradient(
                      colors: [Color(0xFFE6E6FA), Color(0xFFF8BBD9)],
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      imagePath,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF8B5CF6), Color(0xFF3B82F6)],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.auto_awesome,
                            color: Colors.white,
                            size: 32,
                          ),
                        );
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // 右侧文字内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF2D3748),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 导航到专题问题页面
  void _navigateToReading(BuildContext context, String topic, String spreadType) {
    // 获取专题的描述文字
    String subtitle = _getTopicSubtitle(topic, context);

    // 获取适合该专题的牌阵
    final spreads = TarotSpread.getSpreadsForTopic(topic);
    final selectedSpread = spreads.isNotEmpty ? spreads.first : TarotSpread.getSpreadByName('三张牌阵（经典）')!;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicQuestionScreen(
          topicTitle: topic,
          topicSubtitle: subtitle,
          spreadType: selectedSpread.name,
        ),
      ),
    );
  }

  // 获取专题描述
  String _getTopicSubtitle(String topic, BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    switch (topic) {
      case 'Yes or No':
        return languageManager.translate('yes_or_no_subtitle');
      case '二选一抉择':
        return languageManager.translate('two_choice_subtitle');
      case '三选一抉择':
        return languageManager.translate('three_choice_subtitle');
      case '真爱时机':
        return languageManager.translate('true_love_timing_subtitle');
      case '分手复合':
        return languageManager.translate('breakup_reconciliation_subtitle');
      case '第三者问题':
        return languageManager.translate('third_party_problem_subtitle');
      case '暗恋心意':
        return languageManager.translate('secret_crush_subtitle');
      case '人际关系':
        return languageManager.translate('interpersonal_relationships_subtitle');
      case '事业发展':
        return languageManager.translate('career_development_subtitle');
      case '跳槽转职':
        return languageManager.translate('job_change_subtitle');
      case '升职加薪':
        return languageManager.translate('promotion_raise_subtitle');
      case '学业规划':
        return languageManager.translate('academic_planning_subtitle');
      case '考试运势':
        return languageManager.translate('exam_fortune_subtitle');
      case '创业机会':
        return languageManager.translate('entrepreneurship_opportunity_subtitle');
      case '自我认知':
        return languageManager.translate('self_awareness_subtitle');
      case '情绪疗愈':
        return languageManager.translate('emotional_healing_subtitle');
      case '每日灵感':
        return languageManager.translate('daily_inspiration_subtitle');
      case '购物智慧':
        return languageManager.translate('shopping_wisdom_subtitle');
      case '财富运势':
        return languageManager.translate('wealth_fortune_subtitle');
      case '宠物情绪':
        return languageManager.translate('pet_emotions_subtitle');
      case '宠物缘分':
        return languageManager.translate('pet_compatibility_subtitle');
      case '宠物感情':
        return languageManager.translate('pet_affection_subtitle');
      case '宠物陪伴':
        return languageManager.translate('pet_companionship_subtitle');
      case '宠物离别':
        return languageManager.translate('pet_farewell_subtitle');
      default:
        return languageManager.translate('default_tarot_guidance');
    }
  }
}
