import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/diary_entry.dart';
import 'higher_self_memory_service.dart';

class DiaryService {
  static final _supabase = Supabase.instance.client;

  /// 保存日记条目
  static Future<DiaryEntry?> saveDiaryEntry({
    required String content,
    int? moodScore,
    List<String>? tags,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ 用户未登录');
        return null;
      }

      // 1. 保存日记到数据库
      final response = await _supabase
          .from('diary_entries')
          .insert({
            'user_id': user.id,
            'content': content,
            'mood_score': moodScore,
            'tags': tags ?? [],
          })
          .select()
          .single();

      final diaryEntry = DiaryEntry.fromJson(response);

      // 2. 异步处理embedding（不阻塞用户操作）
      _processEmbeddingAsync(diaryEntry);

      return diaryEntry;
    } catch (e) {
      debugPrint('❌ 保存日记失败: $e');
      return null;
    }
  }

  /// 获取用户的日记列表
  static Future<List<DiaryEntry>> getUserDiaries({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 获取日记列表失败: $e');
      return [];
    }
  }

  /// 获取最近的日记（用于高我回忆）
  static Future<List<DiaryEntry>> getRecentDiaries({
    int days = 7,
    int limit = 5,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final cutoffDate = DateTime.now().subtract(Duration(days: days));

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .gte('created_at', cutoffDate.toIso8601String())
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 获取最近日记失败: $e');
      return [];
    }
  }

  /// 根据心情评分获取日记
  static Future<List<DiaryEntry>> getDiariesByMood({
    int? minMood,
    int? maxMood,
    int limit = 10,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      var query = _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .not('mood_score', 'is', null);

      if (minMood != null) {
        query = query.gte('mood_score', minMood);
      }
      if (maxMood != null) {
        query = query.lte('mood_score', maxMood);
      }

      final response = await query
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 按心情获取日记失败: $e');
      return [];
    }
  }

  /// 搜索日记内容
  static Future<List<DiaryEntry>> searchDiaries({
    required String keyword,
    int limit = 10,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .textSearch('content', keyword)
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 搜索日记失败: $e');
      return [];
    }
  }

  /// 获取日记统计信息
  static Future<Map<String, dynamic>> getDiaryStats() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return {};

      // 获取总数
      final countResponse = await _supabase
          .from('diary_entries')
          .select('id')
          .eq('user_id', user.id)
          .count();

      // 获取平均心情
      final moodResponse = await _supabase
          .rpc('get_average_mood', params: {'user_id': user.id});

      // 获取最近7天的日记数量
      final recentDate = DateTime.now().subtract(const Duration(days: 7));
      final recentResponse = await _supabase
          .from('diary_entries')
          .select('id')
          .eq('user_id', user.id)
          .gte('created_at', recentDate.toIso8601String())
          .count();

      return {
        'total_entries': countResponse.count,
        'average_mood': moodResponse ?? 0.0,
        'recent_entries': recentResponse.count,
        'days_active': await _getDaysActive(user.id),
      };
    } catch (e) {
      debugPrint('❌ 获取日记统计失败: $e');
      return {};
    }
  }

  /// 删除日记条目
  static Future<bool> deleteDiaryEntry(String diaryId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase
          .from('diary_entries')
          .delete()
          .eq('id', diaryId)
          .eq('user_id', user.id);

      return true;
    } catch (e) {
      debugPrint('❌ 删除日记失败: $e');
      return false;
    }
  }

  /// 异步处理embedding（不阻塞主线程）
  static void _processEmbeddingAsync(DiaryEntry diary) {
    // 在后台处理embedding
    Future.microtask(() async {
      try {
        await HigherSelfMemoryService.processDiaryEntry(diary);
        debugPrint('✅ 日记embedding处理完成: ${diary.id}');
      } catch (e) {
        debugPrint('❌ 日记embedding处理失败: $e');
      }
    });
  }

  /// 获取用户活跃天数
  static Future<int> _getDaysActive(String userId) async {
    try {
      final response = await _supabase
          .rpc('get_active_days', params: {'user_id': userId});
      return response ?? 0;
    } catch (e) {
      debugPrint('❌ 获取活跃天数失败: $e');
      return 0;
    }
  }

  /// 创建示例日记（用于演示）
  static Future<void> createSampleDiaries() async {
    final sampleEntries = [
      {
        'content': '今天完成了一个重要的项目，虽然过程很辛苦，但看到最终结果时真的很有成就感。团队合作也很愉快，大家都很努力。',
        'mood_score': 8,
        'tags': ['工作', '成就感', '团队合作'],
      },
      {
        'content': '最近总是感到有些焦虑，不知道是不是因为工作压力太大了。希望能找到更好的平衡方式。',
        'mood_score': 4,
        'tags': ['焦虑', '工作压力', '平衡'],
      },
      {
        'content': '和朋友聊天后心情好了很多，有时候分享真的很重要。感谢身边有这样的朋友。',
        'mood_score': 7,
        'tags': ['友谊', '分享', '感恩'],
      },
      {
        'content': '学会了一个新技能，虽然还不熟练，但进步的感觉真好。坚持学习真的会有收获。',
        'mood_score': 7,
        'tags': ['学习', '进步', '坚持'],
      },
      {
        'content': '今天心情有点低落，可能是天气的原因。不过相信明天会更好。',
        'mood_score': 5,
        'tags': ['低落', '天气', '希望'],
      },
    ];

    for (final entry in sampleEntries) {
      await saveDiaryEntry(
        content: entry['content'] as String,
        moodScore: entry['mood_score'] as int,
        tags: List<String>.from(entry['tags'] as List),
      );
      
      // 添加一些延迟，模拟不同时间写的日记
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }
}
