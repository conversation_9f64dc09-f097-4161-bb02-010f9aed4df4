# 🏛️ AI塔罗牌应用架构文档

## 📌 项目概述

AI塔罗牌应用是一个基于Flutter开发的跨平台应用，集成了塔罗牌解读、每日运势、愿望显化等功能。应用采用现代神秘主义设计风格，支持Figma设计集成，并具备完整的用户数据管理和云端同步能力。

## 🏗️ 系统架构

### 1. 分层架构

```
┌───────────────────────────────────────────────────────────────┐
│                        Presentation Layer                     │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                      UI Components                      │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                     State Management                    │  │
│  └─────────────────────────────────────────────────────────┘  │
├───────────────────────────────────────────────────────────────┤
│                         Domain Layer                          │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                     Business Logic                       │  │
│  └─────────────────────────────────────────────────────────┘  │
├───────────────────────────────────────────────────────────────┤
│                         Data Layer                           │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                     Local Storage                       │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                     Remote API                         │  │
│  └─────────────────────────────────────────────────────────┘  │
└───────────────────────────────────────────────────────────────┘
```

### 2. 技术栈

- **前端框架**: Flutter 3.x
- **状态管理**: Provider
- **本地存储**: SQLite (通过sqflite)
- **远程API**: Supabase
- **设计工具**: Figma
- **构建工具**: Flutter CLI

## 🗃️ 数据架构

### 1. 数据库设计

参考 `DATABASE_DESIGN.md` 文件，核心表包括：
- `users`: 用户信息
- `tarot_readings`: 塔罗解读记录
- `daily_tarot`: 每日塔罗记录
- `manifestation_records`: 显化记录
- `user_preferences`: 用户偏好设置

### 2. 数据流

```
用户操作 → 界面事件 → 状态管理 → 业务逻辑 → 数据层 → 本地/远程存储
```

## 🎨 UI架构

### 1. 组件树结构

```
App
├── MaterialApp
│   ├── ThemeData
│   ├── Navigator
│   │   ├── HomeScreen
│   │   │   ├── TarotReadingView
│   │   │   ├── DailyTarotView
│   │   │   └── UserProfileView
│   │   ├── ReadingScreen
│   │   └── SettingsScreen
│   └── Localizations
└── Providers
    ├── AppStateProvider
    └── ThemeProvider
```

### 2. 设计系统

- **颜色方案**: 紫色、粉色、金色渐变
- **字体系统**: Inter字体家族
- **间距系统**: 4px基准倍数
- **动画规范**: 流畅的卡牌动画

## 🔌 服务集成

### 1. Figma集成

- **设计资源管理**: 通过 `figma_assets.dart` 管理
- **设计Token**: 通过 `figma_theme.dart` 实现
- **API集成**: 通过 `figma_service.dart` 调用Figma API

### 2. Supabase集成

- **认证服务**: 用户注册/登录
- **数据存储**: 解读记录同步
- **边缘函数**: 自定义业务逻辑

## ⚙️ 核心模块

### 1. 塔罗牌系统

- **卡牌数据**: 78张标准塔罗牌
- **牌阵逻辑**: 单张、三张、六芒星等
- **解读引擎**: AI解读接口

### 2. 每日塔罗

- **抽卡逻辑**: 随机选择算法
- **运势生成**: 基于卡牌的运势分析
- **历史记录**: 日历视图展示

### 3. 愿望显化

- **目标管理**: 5类显化目标
- **计数器**: 正念计数
- **肯定语**: 自定义肯定语句

## 🚀 部署架构

### 1. 多平台支持

- **iOS**: 通过Xcode构建
- **Android**: 通过Gradle构建
- **Web**: Flutter Web构建
- **Desktop**: 实验性支持

### 2. CI/CD流程

```
代码提交 → GitHub Actions → 构建测试 → 部署到Firebase Hosting (Web)
                                   → 上传到TestFlight (iOS)
                                   → 上传到Google Play (Android)
```

## 📈 性能优化

### 1. 图片加载

- **懒加载**: 按需加载卡牌图片
- **缓存机制**: 内存和磁盘缓存
- **占位符**: 加载中状态处理

### 2. 状态管理

- **局部刷新**: 最小化重绘范围
- **状态持久化**: 保存关键状态
- **性能分析**: DevTools监控

## 🔮 未来扩展

### 1. AI增强

- **深度学习模型**: 更精准的解读
- **自然语言处理**: 更好的交互体验

### 2. 社交功能

- **分享系统**: 解读结果分享
- **社区功能**: 用户交流

### 3. 多平台支持

- **桌面端**: 完整桌面体验
- **AR/VR**: 沉浸式体验

---

📅 **文档版本**: v1.0.0
🔄 **最后更新**: 2025年6月18日
👨‍💻 **维护者**: 项目团队